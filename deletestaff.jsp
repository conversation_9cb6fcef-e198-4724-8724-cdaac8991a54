<%@ page import="java.sql.*" %>
<html>
<body bgcolor="pink" text="blue">
    <table border="1" width="700">
        <tr>
            <th>Staff ID</th>
            <th>Staff Name</th>
            <th>Staff Address</th>
            <th>Contact No</th>
            <th>Email</th>
            <th>Photo</th>
            <th>DOB</th>
            <th>Joining Date</th>
            <th>Action</th>
        </tr>

        <%
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;

        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            con = DriverManager.getConnection("*************************************", "scott", "tiger");
            st = con.createStatement();
            rs = st.executeQuery("SELECT * FROM staff WHERE status='Active'");

            while (rs.next()) {
                String sid = rs.getString(1);
                String photoPath = rs.getString(6); // Adjust if this is not the photo column

                out.println("<tr>");
                out.println("<td>" + sid + "</td>");
                out.println("<td>" + rs.getString(2) + "</td>");
                out.println("<td>" + rs.getString(3) + "</td>");
                out.println("<td>" + rs.getString(4) + "</td>");
                out.println("<td>" + rs.getString(5) + "</td>");
                out.println("<td><img src='" + photoPath + "' width='100' height='100'></td>");
                out.println("<td>" + rs.getString(7) + "</td>");
                out.println("<td>" + rs.getString(8) + "</td>");
                out.println("<td><a href='deletestaff1.jsp?t1=" + sid + "'>Delete</a></td>");
                out.println("</tr>");
            }

        } catch (Exception e) {
            out.println("<tr><td colspan='9' style='color:red;'>Error: " + e.getMessage() + "</td></tr>");
        } finally {
            try {
                if (rs != null) rs.close();
                if (st != null) st.close();
                if (con != null) con.close();
            } catch (Exception e2) {
                out.println("<tr><td colspan='9' style='color:red;'>Cleanup Error: " + e2.getMessage() + "</td></tr>");
            }
        }
        %>
    </table>
</body>
</html>
