<%@ page import="java.sql.*" %>
<html>
<body bgcolor="pink" text="blue">
<%
    try {
        // Fix: Correct method name and spelling
        String s1 = request.getParameter("t1");

        // Fix: Correct spelling of println
        out.println("<h3>Staff ID selected: " + s1 + "</h3>");

        // Load Oracle JDBC driver and connect
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection(
            "*************************************", "scott", "tiger"
        );
        Statement st = con.createStatement();

        // Fix: Proper quote handling in SQL string
        String qry = "SELECT * FROM staff WHERE stid='" + s1 + "'";
        ResultSet rs = st.executeQuery(qry);

        // Begin table output
        out.println("<table border='1'>");

        if (rs.next()) {
            // Output form fields
            out.println("<form action='modifystaff1.jsp' method='post'>");

            out.println("<tr><th>Staff ID</th><td><input type='text' name='t1' value='" + rs.getString(1) + "' readonly></td></tr>");
            out.println("<tr><th>Staff Name</th><td><input type='text' name='t2' value='" + rs.getString(2) + "'></td></tr>");
            out.println("<tr><th>Staff Address</th><td><input type='text' name='t3' value='" + rs.getString(3) + "'></td></tr>");
            out.println("<tr><th>Contact No</th><td><input type='text' name='t4' value='" + rs.getString(4) + "'></td></tr>");
            out.println("<tr><th>Email</th><td><input type='text' name='t5' value='" + rs.getString(5) + "'></td></tr>");
            out.println("<tr><th>Photo</th><td><input type='text' name='t6' value='" + rs.getString(6) + "'></td></tr>");
            out.println("<tr><th>Joining Date</th><td><input type='text' name='t7' value='" + rs.getString(7) + "'></td></tr>");
            out.println("<tr><td colspan='2'><input type='submit' value='Update'></td></tr>");

            out.println("</form>");
        } else {
            out.println("<tr><td colspan='2'>No staff found with ID: " + s1 + "</td></tr>");
        }

        out.println("</table>");

        rs.close();
        st.close();
        con.close();

    } catch (Exception e) {
        out.println("<h3 style='color:red;'>Error: " + e.getMessage() + "</h3>");
    }
%>
</body>
</html>
