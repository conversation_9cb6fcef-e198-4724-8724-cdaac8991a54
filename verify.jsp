<%@ page language="java" import="java.sql.*,javax.servlet.*,javax.servlet.http.*" %>
<html>
<body bgcolor="pink" text="blue">
<%
    try {
        // Load the driver and connect
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection(
            "*************************************", "scott", "tiger"
        );
        Statement st = con.createStatement();

        String utype = request.getParameter("t1");  // user type: Admin / staff
        String uname = request.getParameter("t2");  // username
        String pwd = request.getParameter("t3");    // password

        // Admin login
        if (utype.equalsIgnoreCase("Admin")) {
            if (uname.equals("Admin") && pwd.equals("Admin")) {
                response.sendRedirect("Admin.jsp");
            } else {
                out.println("<p>Invalid Admin Username/Password</p>");
                out.println("<a href='index.jsp'>Click Here</a> to go back");
            }
        }

        // Staff login
        else if (utype.equalsIgnoreCase("staff")) {
            String qry = "SELECT * FROM staff WHERE stid='" + uname + "' AND stid='" + pwd + "' AND status='Active'";
            ResultSet rs = st.executeQuery(qry);

            if (rs.next()) {
                session.setAttribute("stid", rs.getString(1));
                session.setAttribute("stname", rs.getString(2));
                response.sendRedirect("staffpage.jsp");
            } else {
                out.println("<p>Invalid Staff Username/Password</p>");
                out.println("<a href='index.jsp'>Click Here</a> to go back");
            }
        }

        st.close();
        con.close();

    } catch (Exception e) {
        out.println("<p style='color:red;'>Error: " + e.getMessage() + "</p>");
    }
%>
</body>
</html>
