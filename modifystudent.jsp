<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <center>
        <h1>STUDENT UPDATE FORM</h1>
        <form action="modifystudent1.jsp" method="post">
            Select Student ID:
            <select name="studentId" required>
                <option value="">---SELECT STUDENT---</option>

                <%
                try {
                    Class.forName("oracle.jdbc.driver.OracleDriver");
                    Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
                    Statement st = con.createStatement();
                    ResultSet rs = st.executeQuery("SELECT student_id, student_name FROM student WHERE status='Active' ORDER BY student_id");

                    while (rs.next()) {
                        String studentId = rs.getString(1);
                        String studentName = rs.getString(2);
                        out.println("<option value='" + studentId + "'>" + studentId + " - " + studentName + "</option>");
                    }

                    rs.close();
                    st.close();
                    con.close();

                } catch (Exception e) {
                    out.println("<option value=''>Error: " + e.getMessage() + "</option>");
                }
                %>

            </select>
            <br><br>
            <input type="submit" value="SHOW STUDENT DETAILS">
        </form>
        
        <br><br>
        <h3>Available Students for Modification:</h3>
        <table border="1">
            <tr>
                <th>Student ID</th>
                <th>Student Name</th>
                <th>Course</th>
                <th>Year</th>
                <th>Contact</th>
            </tr>
            
            <%
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery("SELECT student_id, student_name, course, year, contact_no FROM student WHERE status='Active' ORDER BY student_id");

                while (rs.next()) {
                    out.println("<tr>");
                    out.println("<td>" + rs.getString(1) + "</td>");
                    out.println("<td>" + rs.getString(2) + "</td>");
                    out.println("<td>" + rs.getString(3) + "</td>");
                    out.println("<td>" + rs.getString(4) + "</td>");
                    out.println("<td>" + rs.getString(5) + "</td>");
                    out.println("</tr>");
                }

                rs.close();
                st.close();
                con.close();

            } catch (Exception e) {
                out.println("<tr><td colspan='5' style='color:red;'>Error: " + e.getMessage() + "</td></tr>");
            }
            %>
        </table>
        
        <br><br>
        <a href="studentinfo.jsp">Back to Student Menu</a>
    </center>
</body>
</html>
