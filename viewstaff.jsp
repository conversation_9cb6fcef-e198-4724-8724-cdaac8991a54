<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    <table border="1" width="700">
        <tr>
            <th>staff Id</th>
            <th>staff Name</th>
            <th>staff Address</th>
            <th>Contact No</th>
            <th>Email</th>
            <th>Photo</th>
            <th>DOB</th>
            <th>Joining Date</th>
        </tr>

        <%
        try {
            // Fix: Class should be capitalized, and semicolon added
            Class.forName("oracle.jdbc.driver.OracleDriver");

            // Fix: semicolon added at end of line
            Connection con = DriverManager.getConnection("***************************************", "scott", "tiger");

            // Fix: Statement should be capitalized, semicolon added
            Statement st = con.createStatement();

            ResultSet rs = st.executeQuery("select * from staff where status='Active'");

            while (rs.next()) {
                String x = rs.getString(5); // Assuming 5 is the photo URL/path

                // Each out.println statement must end with semicolon
                out.println("<tr><td>" + rs.getString(1) + "</td>");
                out.println("<td>" + rs.getString(2) + "</td>");
                out.println("<td>" + rs.getString(3) + "</td>");
                out.println("<td>" + rs.getString(4) + "</td>");
                out.println("<td>" + rs.getString(5) + "</td>");
                out.println("<td><img src='" + x + "' width='100' height='100'></td>");
                out.println("<td>" + rs.getString(6) + "</td>");
                out.println("<td>" + rs.getString(7) + "</td>");
                out.println("</tr>");
            }

            // Fix: semicolons and parentheses corrected here
            rs.close();
            st.close();
            con.close();

        } catch (Exception e) {
            out.println(e);
        }
        %>
    </table>
</body>
</html>
