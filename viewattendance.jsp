<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <center><h1>VIEW ATTENDANCE</h1>
    
    <!-- Filter Form -->
    <form method="get">
        <table border="1">
            <tr>
                <th>Filter by Student ID:</th>
                <td>
                    <select name="filterStudentId">
                        <option value="">--ALL STUDENTS--</option>
                        <%
                        try {
                            Class.forName("oracle.jdbc.driver.OracleDriver");
                            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
                            Statement st = con.createStatement();
                            ResultSet rs = st.executeQuery("SELECT DISTINCT student_id FROM student WHERE status='Active' ORDER BY student_id");
                            
                            String selectedStudent = request.getParameter("filterStudentId");
                            
                            while (rs.next()) {
                                String studentId = rs.getString(1);
                                String selected = (studentId.equals(selectedStudent)) ? "selected" : "";
                                out.println("<option value='" + studentId + "' " + selected + ">" + studentId + "</option>");
                            }
                            
                            rs.close();
                            st.close();
                            con.close();
                        } catch (Exception e) {
                            out.println("<option value=''>Error loading students</option>");
                        }
                        %>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Filter by Subject:</th>
                <td>
                    <select name="filterSubject">
                        <option value="">--ALL SUBJECTS--</option>
                        <%
                        String selectedSubject = request.getParameter("filterSubject");
                        String[] subjects = {"Mathematics", "Physics", "Chemistry", "Biology", "English", "Computer Science", "History", "Geography"};
                        
                        for (String subject : subjects) {
                            String selected = (subject.equals(selectedSubject)) ? "selected" : "";
                            out.println("<option value='" + subject + "' " + selected + ">" + subject + "</option>");
                        }
                        %>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Filter by Date:</th>
                <td><input type="date" name="filterDate" value="<%=request.getParameter("filterDate") != null ? request.getParameter("filterDate") : ""%>"></td>
            </tr>
        </table>
        <br>
        <input type="submit" value="FILTER">
        <input type="button" value="CLEAR" onclick="window.location.href='viewattendance.jsp'">
    </form>
    
    <br><br>
    
    <!-- Attendance Table -->
    <table border="1" width="800">
        <tr>
            <th>Student ID</th>
            <th>Student Name</th>
            <th>Date</th>
            <th>Subject</th>
            <th>Status</th>
            <th>Remarks</th>
        </tr>
        
        <%
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
            
            // Build query based on filters
            String query = "SELECT a.student_id, s.student_name, a.attendance_date, a.subject, a.status, a.remarks " +
                          "FROM attendance a, student s WHERE a.student_id = s.student_id";
            
            String filterStudentId = request.getParameter("filterStudentId");
            String filterSubject = request.getParameter("filterSubject");
            String filterDate = request.getParameter("filterDate");
            
            if (filterStudentId != null && !filterStudentId.trim().equals("")) {
                query += " AND a.student_id = '" + filterStudentId + "'";
            }
            
            if (filterSubject != null && !filterSubject.trim().equals("")) {
                query += " AND a.subject = '" + filterSubject + "'";
            }
            
            if (filterDate != null && !filterDate.trim().equals("")) {
                query += " AND a.attendance_date = '" + filterDate + "'";
            }
            
            query += " ORDER BY a.attendance_date DESC, a.student_id, a.subject";
            
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery(query);
            
            int recordCount = 0;
            while (rs.next()) {
                recordCount++;
                String studentId = rs.getString(1);
                String studentName = rs.getString(2);
                String attendanceDate = rs.getString(3);
                String subject = rs.getString(4);
                String status = rs.getString(5);
                String remarks = rs.getString(6);
                
                out.println("<tr>");
                out.println("<td>" + studentId + "</td>");
                out.println("<td>" + studentName + "</td>");
                out.println("<td>" + attendanceDate + "</td>");
                out.println("<td>" + subject + "</td>");
                out.println("<td>" + status + "</td>");
                out.println("<td>" + (remarks != null ? remarks : "") + "</td>");
                out.println("</tr>");
            }
            
            if (recordCount == 0) {
                out.println("<tr><td colspan='6' align='center'>No attendance records found</td></tr>");
            }
            
            rs.close();
            st.close();
            con.close();
            
        } catch (Exception e) {
            out.println("<tr><td colspan='6' style='color:red;'>Error: " + e.getMessage() + "</td></tr>");
        }
        %>
    </table>
    
    <br><br>
    <a href="setattendance.jsp">Mark New Attendance</a>
    <br><br>
    <a href="studentinfo.jsp">Back to Student Menu</a>
    
    </center>
</body>
</html>
