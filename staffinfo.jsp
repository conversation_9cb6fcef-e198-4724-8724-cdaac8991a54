<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Management - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            padding: 30px;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .menu-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 35px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .menu-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .menu-card:hover::before {
            left: 100%;
        }

        .menu-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .menu-card.add-staff {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(255, 107, 157, 0.3);
        }

        .menu-card.add-staff:hover {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(255, 255, 255, 0.9));
        }

        .menu-card.view-staff {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(78, 205, 196, 0.3);
        }

        .menu-card.view-staff:hover {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(255, 255, 255, 0.9));
        }

        .menu-card.delete-staff {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(255, 107, 107, 0.3);
        }

        .menu-card.delete-staff:hover {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 255, 255, 0.9));
        }

        .menu-card.modify-staff {
            background: linear-gradient(135deg, rgba(150, 206, 180, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(150, 206, 180, 0.3);
        }

        .menu-card.modify-staff:hover {
            background: linear-gradient(135deg, rgba(150, 206, 180, 0.2), rgba(255, 255, 255, 0.9));
        }

        .menu-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .menu-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .menu-description {
            color: #666;
            font-size: 1rem;
            line-height: 1.5;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 50px;
            padding: 12px 20px;
            text-decoration: none;
            color: #333;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .menu-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .menu-card {
                padding: 25px;
            }

            .menu-icon {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="Admin.jsp" class="back-button">← Back to Dashboard</a>

    <div class="container">
        <div class="header">
            <h1>👥 Staff Management</h1>
            <p class="subtitle">Manage all staff-related operations</p>
        </div>

        <div class="menu-grid">
            <a href="addstaff.jsp" class="menu-card add-staff">
                <span class="menu-icon">➕</span>
                <h3 class="menu-title">Add New Staff</h3>
                <p class="menu-description">Register a new staff member with complete details and information</p>
            </a>

            <a href="viewstaff.jsp" class="menu-card view-staff">
                <span class="menu-icon">👁️</span>
                <h3 class="menu-title">View Staff</h3>
                <p class="menu-description">Browse and search through all registered staff members</p>
            </a>

            <a href="deletestaff.jsp" class="menu-card delete-staff">
                <span class="menu-icon">🗑️</span>
                <h3 class="menu-title">Delete Staff</h3>
                <p class="menu-description">Remove staff members from the system permanently</p>
            </a>

            <a href="modifystaff.jsp" class="menu-card modify-staff">
                <span class="menu-icon">✏️</span>
                <h3 class="menu-title">Modify Staff</h3>
                <p class="menu-description">Update and edit existing staff member information</p>
            </a>
        </div>
    </div>
</body>
</html>
