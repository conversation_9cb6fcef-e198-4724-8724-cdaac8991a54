<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <%
    String studentId = request.getParameter("studentId");
    
    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
        
        // First get student details before deletion
        PreparedStatement getPs = con.prepareStatement("SELECT student_name FROM student WHERE student_id = ?");
        getPs.setString(1, studentId);
        ResultSet rs = getPs.executeQuery();
        
        String studentName = "";
        if (rs.next()) {
            studentName = rs.getString(1);
        }
        
        // Delete student record (set status to 'Inactive' instead of actual deletion)
        PreparedStatement ps = con.prepareStatement("UPDATE student SET status = 'Inactive' WHERE student_id = ?");
        ps.setString(1, studentId);
        
        int result = ps.executeUpdate();
        
        if (result > 0) {
            out.println("<center><h2>Student Deleted Successfully!</h2></center>");
            out.println("<center>");
            out.println("<table border='1'>");
            out.println("<tr><th>Deleted Student ID</th><td>" + studentId + "</td></tr>");
            out.println("<tr><th>Deleted Student Name</th><td>" + studentName + "</td></tr>");
            out.println("<tr><th>Status</th><td>Record Deactivated</td></tr>");
            out.println("</table>");
            out.println("<br><br>");
            out.println("<a href='deletestudent.jsp'>Delete Another Student</a>");
            out.println("<br><br>");
            out.println("<a href='viewstudent.jsp'>View All Students</a>");
            out.println("<br><br>");
            out.println("<a href='studentinfo.jsp'>Back to Student Menu</a>");
            out.println("</center>");
        } else {
            out.println("<center><h2>Error: Student not found or already deleted!</h2></center>");
            out.println("<center><a href='deletestudent.jsp'>Go Back</a></center>");
        }
        
        rs.close();
        getPs.close();
        ps.close();
        con.close();
        
    } catch (Exception e) {
        out.println("<center><h2>Error: " + e.getMessage() + "</h2></center>");
        out.println("<center><a href='deletestudent.jsp'>Go Back</a></center>");
    }
    %>
</body>
</html>
