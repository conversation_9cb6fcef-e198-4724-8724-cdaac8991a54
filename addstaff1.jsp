<html>
    <body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*"%>
    <%
    String s1 = request.getParameter("t1");
    String s2 = request.getParameter("t2");
    String s3 = request.getParameter("t3");
    String s4 = request.getParameter("t4");
    String s5 = request.getParameter("t5");
    String s6 = request.getParameter("t6");
    String s7 = request.getParameter("t7");
    String s8=(String)session.getAttribute("date");
    try{

    
    Class.forName("oracle.jdbc.driver.OracleDriver");
Connection con = DriverManager.getConnection("**************************************", "scott", "tiger");
PreparedStatement ps=con.prepareStatement("insert into staff values(?,?,?,?,?,?,?,?,?)");
ps.setString(1,s1);
ps.setString(2,s2);
ps.setString(3,s3);
ps.setString(4,s4);
ps.setString(5,s5);
ps.setString(6,s6);
ps.setString(7,s7);
ps.setString(8,s8);
ps.setString(9,"Active");
ps.executeUpdate();
out.println("Registered Successfully");
    ps.close();
con.close();
    }
    catch(Exception e)
    {
        out.println(e);
    }

    %>
    </body>
</html>
