<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            padding: 30px;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .welcome-container {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .welcome-header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 20px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .welcome-message {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-message h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .welcome-message p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .feature-card.staff {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(255, 107, 157, 0.3);
        }

        .feature-card.student {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(78, 205, 196, 0.3);
        }

        .feature-card.fee {
            background: linear-gradient(135deg, rgba(150, 206, 180, 0.1), rgba(255, 255, 255, 0.9));
            border-color: rgba(150, 206, 180, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-description {
            color: #666;
            font-size: 1rem;
            line-height: 1.5;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .welcome-header h1 {
                font-size: 2.5rem;
            }

            .welcome-message {
                padding: 25px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-header">
            <h1>Welcome to CMS</h1>
        </div>

        <div class="welcome-message">
            <h2>Hello, Administrator!</h2>
            <p>Welcome to the College Management System dashboard. Here you can manage all aspects of your educational institution efficiently and effectively.</p>
            <p>Use the navigation menu to access different modules and manage staff, students, and fees with ease.</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card staff">
                <span class="feature-icon">Staff</span>
                <h3 class="feature-title">Staff Management</h3>
                <p class="feature-description">Add, view, modify, and manage all staff members with comprehensive profiles and information.</p>
            </div>

            <div class="feature-card student">
                <span class="feature-icon">Student</span>
                <h3 class="feature-title">Student Management</h3>
                <p class="feature-description">Handle student registrations, academic records, and maintain detailed student databases.</p>
            </div>

            <div class="feature-card fee">
                <span class="feature-icon">Fee</span>
                <h3 class="feature-title">Fee Management</h3>
                <p class="feature-description">Track fee payments, generate receipts, and manage financial records efficiently.</p>
            </div>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">150+</div>
                <div class="stat-label">Total Staff</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">2,500+</div>
                <div class="stat-label">Students</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">25+</div>
                <div class="stat-label">Departments</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">98%</div>
                <div class="stat-label">Efficiency</div>
            </div>
        </div>
    </div>
</body>
</html>
