<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    <table border="1" width="700">
        <tr>
            <th>staff Id</th>
            <th>staff Name</th>
            <th>staff Address</th>
            <th>Contact No</th>
            <th>Email</th>
            <th>Photo</th>
            <th>DOB</th>
            <th>Joining Date</th>
        </tr>

        <%
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery("select * from staff where status='Active'");

            while (rs.next()) {
                String x = rs.getString(6);
                out.println("<tr><td>" + rs.getString(1) + "</td>");
                out.println("<td>" + rs.getString(2) + "</td>");
                out.println("<td>" + rs.getString(3) + "</td>");
                out.println("<td>" + rs.getString(4) + "</td>");
                out.println("<td>" + rs.getString(5) + "</td>");
                out.println("<td><img src='" + x + "' width='100' height='100'></td>");
                out.println("<td>" + rs.getString(7) + "</td>");
                out.println("<td>" + rs.getString(8) + "</td>");
                out.println("</tr>");
            }

            rs.close();
            st.close();
            con.close();

        } catch (Exception e) {
            out.println(e);
        }
        %>
    </table>
</body>
</html>
