-- Create attendance table for student attendance management
CREATE TABLE attendance (
    attendance_id NUMBER PRIMARY KEY,
    student_id VARCHAR2(20) NOT NULL,
    attendance_date DATE NOT NULL,
    subject VARCHAR2(50) NOT NULL,
    status VARCHAR2(10) NOT NULL CHECK (status IN ('Present', 'Absent')),
    remarks VARCHAR2(200),
    created_date DATE DEFAULT SYSDATE,
    CONSTRAINT unique_attendance UNIQUE (student_id, attendance_date, subject)
);

-- Create sequence for attendance_id
CREATE SEQUENCE attendance_seq
START WITH 1
INCREMENT BY 1
NOCACHE;

-- Create trigger to auto-increment attendance_id
CREATE OR REPLACE TRIGGER attendance_trigger
BEFORE INSERT ON attendance
FOR EACH ROW
BEGIN
    SELECT attendance_seq.NEXTVAL INTO :NEW.attendance_id FROM DUAL;
END;
/

-- Student table with complete structure
CREATE TABLE student (
    student_id VARCHAR2(20) PRIMARY KEY,
    student_name VARCHAR2(100) NOT NULL,
    student_address VARCHAR2(200),
    contact_no VARCHAR2(15),
    email VARCHAR2(100),
    photo VARCHAR2(200),
    dob <PERSON><PERSON>,
    admission_date DATE,
    course VARCHAR2(100),
    year VARCHAR2(20),
    status VARCHAR2(10) DEFAULT 'Active'
);

-- Insert sample student data
INSERT INTO student VALUES ('20001', 'John Smith', '123 Main St', '9876543210', '<EMAIL>', '', TO_DATE('2000-05-15', 'YYYY-MM-DD'), TO_DATE('2023-06-01', 'YYYY-MM-DD'), 'B.Tech Computer Science', '2nd Year', 'Active');
INSERT INTO student VALUES ('20002', 'Jane Doe', '456 Oak Ave', '9876543211', '<EMAIL>', '', TO_DATE('2001-08-22', 'YYYY-MM-DD'), TO_DATE('2023-06-01', 'YYYY-MM-DD'), 'BCA', '3rd Year', 'Active');
INSERT INTO student VALUES ('20003', 'Mike Johnson', '789 Pine Rd', '9876543212', '<EMAIL>', '', TO_DATE('2000-12-10', 'YYYY-MM-DD'), TO_DATE('2023-06-01', 'YYYY-MM-DD'), 'B.Tech Electronics', '1st Year', 'Active');
INSERT INTO student VALUES ('20004', 'Sarah Wilson', '321 Elm St', '9876543213', '<EMAIL>', '', TO_DATE('2001-04-18', 'YYYY-MM-DD'), TO_DATE('2023-06-01', 'YYYY-MM-DD'), 'MBA', '1st Year', 'Active');

COMMIT;
