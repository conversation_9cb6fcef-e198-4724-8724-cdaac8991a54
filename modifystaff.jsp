<%@ page import="java.sql.*" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modify Staff - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 40px;
        }

        .form-container {
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            border-radius: 20px;
            padding: 40px;
            border: 2px solid #E8F4FD;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 30px;
        }

        .form-group label {
            display: block;
            color: #555;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        .form-group select {
            width: 100%;
            padding: 18px 25px;
            border: 2px solid #E8F4FD;
            border-radius: 15px;
            font-size: 1.1rem;
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            transition: all 0.3s ease;
            color: #333;
            cursor: pointer;
        }

        .form-group select:focus {
            outline: none;
            border-color: #4ECDC4;
            background: #FFFFFF;
            box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.1);
            transform: translateY(-2px);
        }

        .submit-btn {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #4ECDC4, #FF6B9D);
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(78, 205, 196, 0.3);
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 50px;
            padding: 12px 20px;
            text-decoration: none;
            color: #333;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            transform: translateY(-2px);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(150, 206, 180, 0.1), rgba(255, 255, 255, 0.9));
            border: 2px solid rgba(150, 206, 180, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .info-box h3 {
            color: #96CEB4;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .info-box p {
            color: #666;
            line-height: 1.6;
        }

        .staff-list {
            background: linear-gradient(145deg, #F8FBFF, #FFFFFF);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border: 2px solid #E8F4FD;
        }

        .staff-list h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .staff-item {
            background: #FFFFFF;
            border: 1px solid #E8F4FD;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .staff-item:hover {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), #FFFFFF);
            transform: translateX(5px);
        }

        .staff-id {
            font-weight: 600;
            color: #4ECDC4;
        }

        .staff-name {
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px;
                margin: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-container {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <a href="staffinfo.jsp" class="back-button">← Back to Staff Menu</a>

    <div class="container">
        <div class="header">
            <h1>Modify Staff</h1>
            <p class="subtitle">Select a staff member to update their information</p>
        </div>

        <div class="info-box">
            <h3>How to Modify Staff</h3>
            <p>Select a staff ID from the dropdown below to view and edit their details. You can update their personal information, contact details, and other relevant data.</p>
        </div>

        <div class="form-container">
            <form action="modifystaff1.jsp" method="post">
                <div class="form-group">
                    <label for="staffSelect">Select Staff ID to Modify:</label>
                    <select name="t1" id="staffSelect" required>
                        <option value="">--- SELECT STAFF MEMBER ---</option>
                        <%
                            try {
                                // Sample staff data for demonstration
                                String[][] sampleStaff = {
                                    {"10001", "John Doe"},
                                    {"10002", "Jane Smith"},
                                    {"10003", "Mike Johnson"},
                                    {"10004", "Sarah Wilson"}
                                };

                                for (String[] staff : sampleStaff) {
                                    out.println("<option value='" + staff[0] + "'>" + staff[0] + " - " + staff[1] + "</option>");
                                }

                            } catch (Exception e) {
                                out.println("<option value=''>Error loading staff: " + e.getMessage() + "</option>");
                            }
                        %>
                    </select>
                </div>

                <button type="submit" class="submit-btn">Show Staff Details</button>
            </form>
        </div>

        <div class="staff-list">
            <h3>Available Staff Members</h3>
            <%
                try {
                    String[][] sampleStaff = {
                        {"10001", "John Doe", "Senior Teacher"},
                        {"10002", "Jane Smith", "Mathematics Professor"},
                        {"10003", "Mike Johnson", "Science Teacher"},
                        {"10004", "Sarah Wilson", "English Teacher"}
                    };

                    for (String[] staff : sampleStaff) {
            %>
                        <div class="staff-item">
                            <div>
                                <span class="staff-id">ID: <%=staff[0]%></span>
                                <span class="staff-name"> - <%=staff[1]%></span>
                            </div>
                            <div class="staff-name"><%=staff[2]%></div>
                        </div>
            <%
                    }
                } catch (Exception e) {
            %>
                    <div style="color: #dc3545; text-align: center; padding: 20px;">
                        Error loading staff list: <%=e.getMessage()%>
                    </div>
            <%
                }
            %>
        </div>
    </div>
</body>
</html>
