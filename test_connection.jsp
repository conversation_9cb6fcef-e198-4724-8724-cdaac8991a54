<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <center><h1>DATABASE CONNECTION TEST</h1>
    
    <h2>Testing Different Connection Strings:</h2>
    
    <%
    String[] connectionStrings = {
        "***********************************",
        "*************************************", 
        "***********************************",
        "*************************************",
        "***********************************",
        "*************************************"
    };
    
    String[] usernames = {"scott", "hr", "system"};
    String[] passwords = {"tiger", "hr", "oracle"};
    
    out.println("<table border='1'>");
    out.println("<tr><th>Connection String</th><th>Username</th><th>Status</th><th>Error</th></tr>");
    
    for (String connStr : connectionStrings) {
        for (int i = 0; i < usernames.length; i++) {
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                Connection con = DriverManager.getConnection(connStr, usernames[i], passwords[i]);
                
                // Test with a simple query
                Statement st = con.createStatement();
                ResultSet rs = st.executeQuery("SELECT 1 FROM DUAL");
                
                out.println("<tr>");
                out.println("<td>" + connStr + "</td>");
                out.println("<td>" + usernames[i] + "/" + passwords[i] + "</td>");
                out.println("<td style='color:green;'><b>SUCCESS</b></td>");
                out.println("<td>Connected successfully!</td>");
                out.println("</tr>");
                
                rs.close();
                st.close();
                con.close();
                
            } catch (Exception e) {
                out.println("<tr>");
                out.println("<td>" + connStr + "</td>");
                out.println("<td>" + usernames[i] + "/" + passwords[i] + "</td>");
                out.println("<td style='color:red;'>FAILED</td>");
                out.println("<td>" + e.getMessage() + "</td>");
                out.println("</tr>");
            }
        }
    }
    
    out.println("</table>");
    %>
    
    <br><br>
    <h3>Common Oracle Database SIDs:</h3>
    <ul>
        <li><b>XE</b> - Oracle Express Edition (most common)</li>
        <li><b>ORCL</b> - Standard Oracle installation</li>
        <li><b>ORCLCDB</b> - Container database</li>
        <li><b>XEPDB1</b> - Pluggable database in XE</li>
    </ul>
    
    <br>
    <h3>How to Find Your Oracle SID:</h3>
    <p>1. Open Command Prompt as Administrator</p>
    <p>2. Run: <code>lsnrctl status</code></p>
    <p>3. Look for "Service" entries in the output</p>
    
    </center>
</body>
</html>
