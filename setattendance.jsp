<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    <%
    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy");
    String currentDate = sdf.format(new java.util.Date()).toString();
    %>
    
    <center><h1>SET ATTENDANCE
        <form action="setattendance1.jsp" method="post">
        <table border="1">
            <tr>
                <th>Date</th>
                <td><input type="date" name="attendanceDate" required></td>
            </tr>
            <tr>
                <th>Student ID</th>
                <td>
                    <select name="studentId" required>
                        <option value="">--SELECT STUDENT--</option>
                        <%
                        try {
                            Class.forName("oracle.jdbc.driver.OracleDriver");
                            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
                            Statement st = con.createStatement();
                            ResultSet rs = st.executeQuery("SELECT student_id, student_name FROM student WHERE status='Active'");
                            
                            while (rs.next()) {
                                String studentId = rs.getString(1);
                                String studentName = rs.getString(2);
                                out.println("<option value='" + studentId + "'>" + studentId + " - " + studentName + "</option>");
                            }
                            
                            rs.close();
                            st.close();
                            con.close();
                        } catch (Exception e) {
                            out.println("<option value=''>Error loading students</option>");
                        }
                        %>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Subject</th>
                <td>
                    <select name="subject" required>
                        <option value="">--SELECT SUBJECT--</option>
                        <option value="Mathematics">Mathematics</option>
                        <option value="Physics">Physics</option>
                        <option value="Chemistry">Chemistry</option>
                        <option value="Biology">Biology</option>
                        <option value="English">English</option>
                        <option value="Computer Science">Computer Science</option>
                        <option value="History">History</option>
                        <option value="Geography">Geography</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Attendance Status</th>
                <td>
                    <input type="radio" name="status" value="Present" required> Present
                    <input type="radio" name="status" value="Absent" required> Absent
                </td>
            </tr>
            <tr>
                <th>Remarks</th>
                <td><input type="text" name="remarks" placeholder="Optional remarks"></td>
            </tr>
        </table>
        <br>
        <input type="submit" value="MARK ATTENDANCE">
        </form>
    </h1></center>
</body>
</html>
