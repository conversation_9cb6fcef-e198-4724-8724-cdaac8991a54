<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    <%@ page import="java.io.*" %>
    <%@ page import="javax.servlet.http.Part" %>
    
    <%
    String studentId = request.getParameter("t1");
    String studentName = request.getParameter("t2");
    String studentAddress = request.getParameter("t3");
    String contactNo = request.getParameter("t4");
    String email = request.getParameter("t5");
    String dob = request.getParameter("t7");
    String course = request.getParameter("t9");
    String year = request.getParameter("t10");
    String admissionDate = (String) session.getAttribute("admissionDate");
    
    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
        
        // Get next student ID
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("SELECT MAX(TO_NUMBER(student_id)) FROM student");
        int nextId = 20001;
        if (rs.next() && rs.getString(1) != null) {
            nextId = rs.getInt(1) + 1;
        }
        
        // Insert student record
        PreparedStatement ps = con.prepareStatement(
            "INSERT INTO student (student_id, student_name, student_address, contact_no, email, photo, dob, admission_date, course, year, status) VALUES (?, ?, ?, ?, ?, ?, TO_DATE(?, 'YYYY-MM-DD'), TO_DATE(?, 'DD/MM/YYYY'), ?, ?, 'Active')"
        );
        
        ps.setString(1, String.valueOf(nextId));
        ps.setString(2, studentName);
        ps.setString(3, studentAddress);
        ps.setString(4, contactNo);
        ps.setString(5, email);
        ps.setString(6, ""); // Photo path - can be enhanced later
        ps.setString(7, dob);
        ps.setString(8, admissionDate);
        ps.setString(9, course);
        ps.setString(10, year);
        
        int result = ps.executeUpdate();
        
        if (result > 0) {
            out.println("<center><h2>Student Registered Successfully!</h2></center>");
            out.println("<center>");
            out.println("<table border='1'>");
            out.println("<tr><th>Student ID</th><td>" + nextId + "</td></tr>");
            out.println("<tr><th>Student Name</th><td>" + studentName + "</td></tr>");
            out.println("<tr><th>Address</th><td>" + studentAddress + "</td></tr>");
            out.println("<tr><th>Contact No</th><td>" + contactNo + "</td></tr>");
            out.println("<tr><th>Email</th><td>" + email + "</td></tr>");
            out.println("<tr><th>Date of Birth</th><td>" + dob + "</td></tr>");
            out.println("<tr><th>Course</th><td>" + course + "</td></tr>");
            out.println("<tr><th>Year</th><td>" + year + "</td></tr>");
            out.println("<tr><th>Admission Date</th><td>" + admissionDate + "</td></tr>");
            out.println("</table>");
            out.println("<br><br>");
            out.println("<a href='addstudent.jsp'>Add Another Student</a>");
            out.println("<br><br>");
            out.println("<a href='viewstudent.jsp'>View All Students</a>");
            out.println("<br><br>");
            out.println("<a href='studentinfo.jsp'>Back to Student Menu</a>");
            out.println("</center>");
        } else {
            out.println("<center><h2>Error: Student registration failed!</h2></center>");
            out.println("<center><a href='addstudent.jsp'>Go Back</a></center>");
        }
        
        ps.close();
        rs.close();
        st.close();
        con.close();
        
    } catch (Exception e) {
        out.println("<center><h2>Error: " + e.getMessage() + "</h2></center>");
        out.println("<center><a href='addstudent.jsp'>Go Back</a></center>");
    }
    %>
</body>
</html>
