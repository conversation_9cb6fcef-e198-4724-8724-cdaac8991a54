<%!
// Database Configuration - Update these values based on your Oracle setup
public class DatabaseConfig {
    
    // Try these connection strings one by one
    public static final String[] DB_URLS = {
        "***********************************",        // Oracle XE (most common)
        "***********************************",        // Oracle XE uppercase
        "*************************************",      // Standard Oracle
        "*************************************",      // Standard Oracle uppercase
        "***********************************",       // Using IP instead of localhost
        "***********************************PDB1"     // Pluggable database
    };
    
    // Try these username/password combinations
    public static final String[][] CREDENTIALS = {
        {"scott", "tiger"},
        {"hr", "hr"},
        {"system", "oracle"},
        {"system", "manager"},
        {"sys", "oracle"}
    };
    
    public static Connection getConnection() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // Try each connection string with each credential
        for (String url : DB_URLS) {
            for (String[] cred : CREDENTIALS) {
                try {
                    Connection con = DriverManager.getConnection(url, cred[0], cred[1]);
                    // Test the connection
                    Statement st = con.createStatement();
                    ResultSet rs = st.executeQuery("SELECT 1 FROM DUAL");
                    rs.close();
                    st.close();
                    return con; // Return successful connection
                } catch (Exception e) {
                    // Continue to next combination
                    continue;
                }
            }
        }
        
        // If no connection works, throw exception
        throw new SQLException("Could not establish database connection. Please check Oracle installation and configuration.");
    }
    
    public static String getWorkingConnectionInfo() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            for (String url : DB_URLS) {
                for (String[] cred : CREDENTIALS) {
                    try {
                        Connection con = DriverManager.getConnection(url, cred[0], cred[1]);
                        Statement st = con.createStatement();
                        ResultSet rs = st.executeQuery("SELECT 1 FROM DUAL");
                        rs.close();
                        st.close();
                        con.close();
                        return "Working Connection: " + url + " with " + cred[0] + "/" + cred[1];
                    } catch (Exception e) {
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            return "No working connection found: " + e.getMessage();
        }
        
        return "No working connection found";
    }
}
%>
