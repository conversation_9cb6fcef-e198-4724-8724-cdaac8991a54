<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            animation: gradientShift 10s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .dashboard-container {
            display: grid;
            grid-template-rows: auto 1fr;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 3px solid rgba(78, 205, 196, 0.3);
        }

        .header h1 {
            text-align: center;
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .welcome-text {
            text-align: center;
            color: #666;
            font-size: 1.2rem;
            margin-top: 10px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            height: fit-content;
        }

        .sidebar h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.5rem;
            text-align: center;
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-menu li {
            margin-bottom: 15px;
        }

        .nav-menu a {
            display: block;
            padding: 15px 20px;
            text-decoration: none;
            color: #555;
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            border-radius: 15px;
            border: 2px solid #E8F4FD;
            transition: all 0.3s ease;
            font-weight: 600;
            text-align: center;
        }

        .nav-menu a:hover {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-content {
            text-align: center;
            padding: 60px 20px;
        }

        .welcome-content h2 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .welcome-content p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .feature-card {
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid #E8F4FD;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .feature-card p {
            color: #666;
            font-size: 0.95rem;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 10px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .sidebar {
                order: 2;
            }

            .content-area {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>🎓 COLLEGE MANAGEMENT SYSTEM</h1>
            <p class="welcome-text">Welcome, Administrator! 👨‍💼</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h2>📋 Admin Menu</h2>
                <ul class="nav-menu">
                    <li><a href="staffinfo.jsp" target="content">👥 Staff Management</a></li>
                    <li><a href="studentinfo.jsp" target="content">🎓 Student Management</a></li>
                    <li><a href="feeinput.jsp" target="content">💰 Fee Management</a></li>
                    <li><a href="index.jsp" target="_top">🚪 Logout</a></li>
                </ul>
            </div>

            <div class="content-area">
                <iframe name="content" src="welcome.jsp" style="width: 100%; height: 100%; border: none; border-radius: 15px;"></iframe>
            </div>
        </div>
    </div>
</body>
</html>