<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <center><h1>DELETE STUDENT</h1>
    <h3 style="color:red;">WARNING: This will permanently remove the student record!</h3>
    
    <table border="1" width="1000">
        <tr>
            <th>Student ID</th>
            <th>Student Name</th>
            <th>Address</th>
            <th>Contact No</th>
            <th>Email</th>
            <th>Course</th>
            <th>Year</th>
            <th>Admission Date</th>
            <th>Action</th>
        </tr>

        <%
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery("SELECT * FROM student WHERE status='Active' ORDER BY student_id");

            int studentCount = 0;
            while (rs.next()) {
                studentCount++;
                String studentId = rs.getString(1);
                String studentName = rs.getString(2);
                String address = rs.getString(3);
                String contactNo = rs.getString(4);
                String email = rs.getString(5);
                String course = rs.getString(9);
                String year = rs.getString(10);
                String admissionDate = rs.getString(8);

                out.println("<tr>");
                out.println("<td>" + studentId + "</td>");
                out.println("<td>" + studentName + "</td>");
                out.println("<td>" + address + "</td>");
                out.println("<td>" + contactNo + "</td>");
                out.println("<td>" + email + "</td>");
                out.println("<td>" + course + "</td>");
                out.println("<td>" + year + "</td>");
                out.println("<td>" + admissionDate + "</td>");
                out.println("<td><a href='deletestudent1.jsp?studentId=" + studentId + "' onclick=\"return confirm('Are you sure you want to delete " + studentName + "? This action cannot be undone!')\">DELETE</a></td>");
                out.println("</tr>");
            }

            if (studentCount == 0) {
                out.println("<tr><td colspan='9' align='center'>No students found</td></tr>");
            } else {
                out.println("<tr><td colspan='9' align='center'><b>Total Students Available for Deletion: " + studentCount + "</b></td></tr>");
            }

            rs.close();
            st.close();
            con.close();

        } catch (Exception e) {
            out.println("<tr><td colspan='9' style='color:red;'>Error: " + e.getMessage() + "</td></tr>");
        }
        %>
    </table>
    
    <br><br>
    <a href="viewstudent.jsp">View All Students</a>
    <br><br>
    <a href="studentinfo.jsp">Back to Student Menu</a>
    
    </center>
</body>
</html>
