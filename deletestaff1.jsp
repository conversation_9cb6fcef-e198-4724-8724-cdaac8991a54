<%@ page import="java.sql.*" %>
<html>
    <body bgcolor="pink" text="blue">
        <center><h1>

        <%
        String s1 = request.getParameter("t1"); // Get staff ID

        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger"); // Fix: 1521 instead of 152
            Statement st = con.createStatement();

            String qry = "UPDATE staff SET status = 'deactive' WHERE stid = '" + s1 + "'";
            st.executeUpdate(qry);

            out.println(s1 + " Deactivated Successfully");

            st.close();
            con.close();
        } catch (Exception e) {
            out.println("Error: " + e.getMessage());
        }
        %>

        </h1></center>
    </body>
</html>
