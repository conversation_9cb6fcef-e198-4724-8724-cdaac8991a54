<%@ page import="java.sql.*" %>
<%@ page import="java.io.*" %>
<%@ page import="javax.servlet.http.HttpSession" %>
<html>
<body bgcolor="pink" text="blue">

<%
String studentId = request.getParameter("t1");
String studentName = request.getParameter("t2");
String studentAddress = request.getParameter("t3");
String contactNo = request.getParameter("t4");
String email = request.getParameter("t5");
String dob = request.getParameter("t7"); // Assume format: YYYY-MM-DD
String course = request.getParameter("t9");
String year = request.getParameter("t10");

HttpSession session = request.getSession(false); // Ensure session exists
String admissionDate = (session != null) ? (String) session.getAttribute("admissionDate") : null;

Connection con = null;
PreparedStatement ps = null;
Statement st = null;
ResultSet rs = null;

try {
    String[] urls = {
        "***********************************",
        "***********************************",
        "*************************************",
        "*************************************"
    };

    String[][] credentials = {
        {"scott", "tiger"},
        {"hr", "hr"},
        {"system", "oracle"}
    };

    Class.forName("oracle.jdbc.driver.OracleDriver");
    boolean connected = false;

    for (String url : urls) {
        for (String[] cred : credentials) {
            try {
                con = DriverManager.getConnection(url, cred[0], cred[1]);
                connected = true;
                break;
            } catch (SQLException e) {
                continue;
            }
        }
        if (connected) break;
    }

    if (!connected) throw new SQLException("Database connection failed.");

    st = con.createStatement();
    rs = st.executeQuery("SELECT MAX(TO_NUMBER(student_id)) FROM student");
    int nextId = 20001;
    if (rs.next() && rs.getString(1) != null) {
        nextId = rs.getInt(1) + 1;
    }

    ps = con.prepareStatement(
        "INSERT INTO student (student_id, student_name, student_address, contact_no, email, photo, dob, admission_date, course, year, status) " +
        "VALUES (?, ?, ?, ?, ?, ?, TO_DATE(?, 'YYYY-MM-DD'), TO_DATE(?, 'DD/MM/YYYY'), ?, ?, 'Active')"
    );

    ps.setString(1, String.valueOf(nextId));
    ps.setString(2, studentName);
    ps.setString(3, studentAddress);
    ps.setString(4, contactNo);
    ps.setString(5, email);
    ps.setString(6, ""); // Placeholder for photo
    ps.setString(7, dob);
    ps.setString(8, admissionDate);
    ps.setString(9, course);
    ps.setString(10, year);

    int result = ps.executeUpdate();

    if (result > 0) {
%>
    <center><h2>Student Registered Successfully!</h2></center>
    <center>
    <table border='1'>
        <tr><th>Student ID</th><td><%= nextId %></td></tr>
        <tr><th>Student Name</th><td><%= studentName %></td></tr>
        <tr><th>Address</th><td><%= studentAddress %></td></tr>
        <tr><th>Contact No</th><td><%= contactNo %></td></tr>
        <tr><th>Email</th><td><%= email %></td></tr>
        <tr><th>Date of Birth</th><td><%= dob %></td></tr>
        <tr><th>Course</th><td><%= course %></td></tr>
        <tr><th>Year</th><td><%= year %></td></tr>
        <tr><th>Admission Date</th><td><%= admissionDate %></td></tr>
    </table>
    <br><br>
    <a href='addstudent.jsp'>Add Another Student</a><br><br>
    <a href='viewstudent.jsp'>View All Students</a><br><br>
    <a href='studentinfo.jsp'>Back to Student Menu</a>
    </center>
<%
    } else {
%>
    <center><h2>Error: Student registration failed!</h2></center>
    <center><a href='addstudent.jsp'>Go Back</a></center>
<%
    }

} catch (Exception e) {
%>
    <center><h2>Error: <%= e.getMessage() %></h2></center>
    <center><a href='addstudent.jsp'>Go Back</a></center>
<%
} finally {
    try { if (rs != null) rs.close(); } catch (Exception ignored) {}
    try { if (st != null) st.close(); } catch (Exception ignored) {}
    try { if (ps != null) ps.close(); } catch (Exception ignored) {}
    try { if (con != null) con.close(); } catch (Exception ignored) {}
}
%>

</body>
</html>
