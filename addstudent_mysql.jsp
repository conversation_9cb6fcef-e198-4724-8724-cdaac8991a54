<html>
<body bgcolor="pink" text="blue">
    <form action="addstudent1_mysql.jsp" method="post" enctype="multipart/form-data">
    <center><h1>ADD NEW STUDENT (MySQL Version)
        <table border="1">
        <tr>
            <th>Student ID</th>
            <td><input type="text" name="t1" value="20001" readonly></td>
        </tr>
        <tr>
            <th>Student Name</th>
            <td><input type="text" name="t2" required></td>
        </tr>
        <tr>
            <th>Student Address</th>
            <td><input type="text" name="t3" required></td>
        </tr>
        <tr>
            <th>Contact No</th>
            <td><input type="text" name="t4" required></td>
        </tr>
        <tr>
            <th>Email</th>
            <td><input type="email" name="t5" required></td>
        </tr>
        <tr>
            <th>Photo</th>
            <td><input type="file" name="t6"></td>
        </tr>
        <tr>
            <th>Date of Birth</th>
            <td><input type="date" name="t7" required></td>
        </tr>
        <tr>
            <th>Admission Date</th>
        <%
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
        String str = sdf.format(new java.util.Date()).toString();
        session.setAttribute("admissionDate", str);
        %>
        <td><input type="text" name="t8" value="<%=str%>" disabled></td>
        </tr>
        <tr>
            <th>Course</th>
            <td>
                <select name="t9" required>
                    <option value="">--SELECT COURSE--</option>
                    <option value="B.Tech Computer Science">B.Tech Computer Science</option>
                    <option value="B.Tech Electronics">B.Tech Electronics</option>
                    <option value="B.Tech Mechanical">B.Tech Mechanical</option>
                    <option value="B.Tech Civil">B.Tech Civil</option>
                    <option value="BCA">BCA</option>
                    <option value="MCA">MCA</option>
                    <option value="MBA">MBA</option>
                </select>
            </td>
        </tr>
        <tr>
            <th>Year</th>
            <td>
                <select name="t10" required>
                    <option value="">--SELECT YEAR--</option>
                    <option value="1st Year">1st Year</option>
                    <option value="2nd Year">2nd Year</option>
                    <option value="3rd Year">3rd Year</option>
                    <option value="4th Year">4th Year</option>
                </select>
            </td>
        </tr>
        </table>
        <br>
        <input type="submit" value="REGISTER STUDENT">
        <input type="reset" value="CLEAR">
    </form>
    <br>
    <a href="studentinfo.jsp">Back to Student Menu</a>
    </h1></center>
</body>
</html>
