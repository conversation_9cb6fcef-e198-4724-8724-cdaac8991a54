class InsertDemo3 {
    public static void main(String[] args) {
	try{

	Class.forName("oracle.jdbc.driver.OracleDriver");
	Connection con = DriverManager.getConnection("********************************:<PERSON><PERSON>","scott","tiger");

	preparedStatement pst =con.prepareStatement("insert into student values(?,?,?)");
	Scanner sc=new Scanner(System.in);
	int option=0;
	
	do{
		System.out.print("Enter Student no:");
		int no = sc.nextInt();
		System.out.print("Enter Student name");
		String name = sc.next();
		System.out.print("Enter Student marks:");
		int marks = sc.nextInt();
	

	    	pst.setInt(1,no);
		pst.setString(2,name);
		pst.setInt(3,marks);

		int x=pst.executeUpdate();
		System.out.println(x+" records inserted");
		System.out.prinln("do you want to continue 0/1?");
		option=sc.nextInt();
	}
	while(option==1);
	pst.close();
	con.close();
	}
	catch(Exception e){
	System.out.println("Exception")
	}
 		
    }
}(sys as sysdba)
