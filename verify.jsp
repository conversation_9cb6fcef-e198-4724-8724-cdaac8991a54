<%@ page language="java" import="java.sql.*,javax.servlet.*,javax.servlet.http.*" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Verification - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .verification-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(78, 205, 196, 0.3);
            border-top: 4px solid #4ECDC4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 30px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .verification-title {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 20px;
        }

        .success-message {
            color: #28a745;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .error-message {
            color: #dc3545;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .back-link {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            text-decoration: none;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .back-link:hover {
            background: linear-gradient(135deg, #4ECDC4, #FF6B9D);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
        }

        .redirecting-text {
            color: #666;
            font-size: 1.1rem;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <%
            try {
                String utype = request.getParameter("t1");
                String uname = request.getParameter("t2");
                String pwd = request.getParameter("t3");

                boolean loginSuccess = false;
                String errorMessage = "";

                // Simple authentication without database for now
                if (utype != null && utype.equalsIgnoreCase("Admin")) {
                    if (uname != null && uname.equals("Admin") && pwd != null && pwd.equals("Admin")) {
                        loginSuccess = true;
        %>
                        <div class="loading-spinner"></div>
                        <h2 class="verification-title">Login Successful!</h2>
                        <p class="success-message">Welcome, Administrator!</p>
                        <p class="redirecting-text">Redirecting to dashboard...</p>
                        <script>
                            setTimeout(function() {
                                window.location.href = 'Admin.jsp';
                            }, 2000);
                        </script>
        <%
                    } else {
                        errorMessage = "Invalid Admin Username/Password";
                    }
                } else if (utype != null && utype.equalsIgnoreCase("Staff")) {
                    // For now, simple staff authentication
                    if (uname != null && pwd != null && uname.equals("staff") && pwd.equals("staff")) {
                        loginSuccess = true;
        %>
                        <div class="loading-spinner"></div>
                        <h2 class="verification-title">Login Successful!</h2>
                        <p class="success-message">Welcome, Staff Member!</p>
                        <p class="redirecting-text">Redirecting to staff dashboard...</p>
                        <script>
                            setTimeout(function() {
                                window.location.href = 'staffpage.jsp';
                            }, 2000);
                        </script>
        <%
                    } else {
                        errorMessage = "Invalid Staff Username/Password";
                    }
                } else if (utype != null && utype.equalsIgnoreCase("Student")) {
                    // Simple student authentication
                    if (uname != null && pwd != null && uname.equals("student") && pwd.equals("student")) {
                        loginSuccess = true;
        %>
                        <div class="loading-spinner"></div>
                        <h2 class="verification-title">Login Successful!</h2>
                        <p class="success-message">Welcome, Student!</p>
                        <p class="redirecting-text">Redirecting to student portal...</p>
                        <script>
                            setTimeout(function() {
                                window.location.href = 'studentpage.jsp';
                            }, 2000);
                        </script>
        <%
                    } else {
                        errorMessage = "Invalid Student Username/Password";
                    }
                } else {
                    errorMessage = "Please select a valid user type";
                }

                if (!loginSuccess && !errorMessage.isEmpty()) {
        %>
                    <h2 class="verification-title">Login Failed</h2>
                    <p class="error-message"><%=errorMessage%></p>
                    <a href="index.jsp" class="back-link">Back to Login</a>
        <%
                }
            } catch (Exception e) {
        %>
                <h2 class="verification-title">System Error</h2>
                <p class="error-message">An error occurred: <%=e.getMessage()%></p>
                <a href="index.jsp" class="back-link">Back to Login</a>
        <%
            }
        %>
    </div>
</body>
</html>
