<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    <%
    String attendanceDate = request.getParameter("attendanceDate");
    String studentId = request.getParameter("studentId");
    String subject = request.getParameter("subject");
    String status = request.getParameter("status");
    String remarks = request.getParameter("remarks");
    
    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
        
        // Check if attendance already exists for this student, date, and subject
        PreparedStatement checkPs = con.prepareStatement("SELECT COUNT(*) FROM attendance WHERE student_id=? AND attendance_date=? AND subject=?");
        checkPs.setString(1, studentId);
        checkPs.setString(2, attendanceDate);
        checkPs.setString(3, subject);
        ResultSet checkRs = checkPs.executeQuery();
        
        if (checkRs.next() && checkRs.getInt(1) > 0) {
            // Update existing attendance
            PreparedStatement updatePs = con.prepareStatement("UPDATE attendance SET status=?, remarks=? WHERE student_id=? AND attendance_date=? AND subject=?");
            updatePs.setString(1, status);
            updatePs.setString(2, remarks);
            updatePs.setString(3, studentId);
            updatePs.setString(4, attendanceDate);
            updatePs.setString(5, subject);
            updatePs.executeUpdate();
            out.println("<center><h2>Attendance Updated Successfully!</h2></center>");
            updatePs.close();
        } else {
            // Insert new attendance record
            PreparedStatement insertPs = con.prepareStatement("INSERT INTO attendance (student_id, attendance_date, subject, status, remarks) VALUES (?, ?, ?, ?, ?)");
            insertPs.setString(1, studentId);
            insertPs.setString(2, attendanceDate);
            insertPs.setString(3, subject);
            insertPs.setString(4, status);
            insertPs.setString(5, remarks);
            insertPs.executeUpdate();
            out.println("<center><h2>Attendance Marked Successfully!</h2></center>");
            insertPs.close();
        }
        
        // Display attendance details
        out.println("<center>");
        out.println("<table border='1'>");
        out.println("<tr><th>Student ID</th><td>" + studentId + "</td></tr>");
        out.println("<tr><th>Date</th><td>" + attendanceDate + "</td></tr>");
        out.println("<tr><th>Subject</th><td>" + subject + "</td></tr>");
        out.println("<tr><th>Status</th><td>" + status + "</td></tr>");
        out.println("<tr><th>Remarks</th><td>" + (remarks != null ? remarks : "None") + "</td></tr>");
        out.println("</table>");
        out.println("<br><br>");
        out.println("<a href='setattendance.jsp'>Mark Another Attendance</a>");
        out.println("<br><br>");
        out.println("<a href='viewattendance.jsp'>View Attendance</a>");
        out.println("</center>");
        
        checkRs.close();
        checkPs.close();
        con.close();
        
    } catch (Exception e) {
        out.println("<center><h2>Error: " + e.getMessage() + "</h2></center>");
        out.println("<center><a href='setattendance.jsp'>Go Back</a></center>");
    }
    %>
</body>
</html>
