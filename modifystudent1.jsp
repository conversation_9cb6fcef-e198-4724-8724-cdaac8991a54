<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <%
    String studentId = request.getParameter("studentId");
    
    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
        
        PreparedStatement ps = con.prepareStatement("SELECT * FROM student WHERE student_id = ? AND status='Active'");
        ps.setString(1, studentId);
        ResultSet rs = ps.executeQuery();
        
        if (rs.next()) {
            String studentName = rs.getString(2);
            String address = rs.getString(3);
            String contactNo = rs.getString(4);
            String email = rs.getString(5);
            String dob = rs.getString(7);
            String course = rs.getString(9);
            String year = rs.getString(10);
    %>
    
    <center><h1>MODIFY STUDENT DETAILS</h1>
    
    <form action="modifystudent2.jsp" method="post">
        <table border="1">
            <tr>
                <th>Student ID</th>
                <td><input type="text" name="studentId" value="<%=studentId%>" readonly></td>
            </tr>
            <tr>
                <th>Student Name</th>
                <td><input type="text" name="studentName" value="<%=studentName%>" required></td>
            </tr>
            <tr>
                <th>Address</th>
                <td><input type="text" name="address" value="<%=address%>" required></td>
            </tr>
            <tr>
                <th>Contact No</th>
                <td><input type="text" name="contactNo" value="<%=contactNo%>" required></td>
            </tr>
            <tr>
                <th>Email</th>
                <td><input type="email" name="email" value="<%=email%>" required></td>
            </tr>
            <tr>
                <th>Date of Birth</th>
                <td><input type="date" name="dob" value="<%=dob%>" required></td>
            </tr>
            <tr>
                <th>Course</th>
                <td>
                    <select name="course" required>
                        <option value="B.Tech Computer Science" <%=course.equals("B.Tech Computer Science") ? "selected" : ""%>>B.Tech Computer Science</option>
                        <option value="B.Tech Electronics" <%=course.equals("B.Tech Electronics") ? "selected" : ""%>>B.Tech Electronics</option>
                        <option value="B.Tech Mechanical" <%=course.equals("B.Tech Mechanical") ? "selected" : ""%>>B.Tech Mechanical</option>
                        <option value="B.Tech Civil" <%=course.equals("B.Tech Civil") ? "selected" : ""%>>B.Tech Civil</option>
                        <option value="BCA" <%=course.equals("BCA") ? "selected" : ""%>>BCA</option>
                        <option value="MCA" <%=course.equals("MCA") ? "selected" : ""%>>MCA</option>
                        <option value="MBA" <%=course.equals("MBA") ? "selected" : ""%>>MBA</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Year</th>
                <td>
                    <select name="year" required>
                        <option value="1st Year" <%=year.equals("1st Year") ? "selected" : ""%>>1st Year</option>
                        <option value="2nd Year" <%=year.equals("2nd Year") ? "selected" : ""%>>2nd Year</option>
                        <option value="3rd Year" <%=year.equals("3rd Year") ? "selected" : ""%>>3rd Year</option>
                        <option value="4th Year" <%=year.equals("4th Year") ? "selected" : ""%>>4th Year</option>
                    </select>
                </td>
            </tr>
        </table>
        <br>
        <input type="submit" value="UPDATE STUDENT">
        <input type="button" value="CANCEL" onclick="window.location.href='modifystudent.jsp'">
    </form>
    </center>
    
    <%
        } else {
            out.println("<center><h2>Student not found!</h2></center>");
            out.println("<center><a href='modifystudent.jsp'>Go Back</a></center>");
        }
        
        rs.close();
        ps.close();
        con.close();
        
    } catch (Exception e) {
        out.println("<center><h2>Error: " + e.getMessage() + "</h2></center>");
        out.println("<center><a href='modifystudent.jsp'>Go Back</a></center>");
    }
    %>
</body>
</html>
