<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <%
    String studentId = request.getParameter("studentId");
    String studentName = request.getParameter("studentName");
    String address = request.getParameter("address");
    String contactNo = request.getParameter("contactNo");
    String email = request.getParameter("email");
    String dob = request.getParameter("dob");
    String course = request.getParameter("course");
    String year = request.getParameter("year");
    
    try {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
        
        PreparedStatement ps = con.prepareStatement(
            "UPDATE student SET student_name=?, student_address=?, contact_no=?, email=?, dob=TO_DATE(?, 'YYYY-MM-DD'), course=?, year=? WHERE student_id=?"
        );
        
        ps.setString(1, studentName);
        ps.setString(2, address);
        ps.setString(3, contactNo);
        ps.setString(4, email);
        ps.setString(5, dob);
        ps.setString(6, course);
        ps.setString(7, year);
        ps.setString(8, studentId);
        
        int result = ps.executeUpdate();
        
        if (result > 0) {
            out.println("<center><h2>Student Updated Successfully!</h2></center>");
            out.println("<center>");
            out.println("<table border='1'>");
            out.println("<tr><th>Student ID</th><td>" + studentId + "</td></tr>");
            out.println("<tr><th>Student Name</th><td>" + studentName + "</td></tr>");
            out.println("<tr><th>Address</th><td>" + address + "</td></tr>");
            out.println("<tr><th>Contact No</th><td>" + contactNo + "</td></tr>");
            out.println("<tr><th>Email</th><td>" + email + "</td></tr>");
            out.println("<tr><th>Date of Birth</th><td>" + dob + "</td></tr>");
            out.println("<tr><th>Course</th><td>" + course + "</td></tr>");
            out.println("<tr><th>Year</th><td>" + year + "</td></tr>");
            out.println("</table>");
            out.println("<br><br>");
            out.println("<a href='modifystudent.jsp'>Modify Another Student</a>");
            out.println("<br><br>");
            out.println("<a href='viewstudent.jsp'>View All Students</a>");
            out.println("<br><br>");
            out.println("<a href='studentinfo.jsp'>Back to Student Menu</a>");
            out.println("</center>");
        } else {
            out.println("<center><h2>Error: Student update failed!</h2></center>");
            out.println("<center><a href='modifystudent.jsp'>Go Back</a></center>");
        }
        
        ps.close();
        con.close();
        
    } catch (Exception e) {
        out.println("<center><h2>Error: " + e.getMessage() + "</h2></center>");
        out.println("<center><a href='modifystudent.jsp'>Go Back</a></center>");
    }
    %>
</body>
</html>
