<html>
<body bgcolor="pink" text="blue">
    <%@ page import="java.sql.*" %>
    
    <center><h1>VIEW ALL STUDENTS</h1>
    
    <table border="1" width="1000">
        <tr>
            <th>Student ID</th>
            <th>Student Name</th>
            <th>Address</th>
            <th>Contact No</th>
            <th>Email</th>
            <th>Photo</th>
            <th>DOB</th>
            <th>Course</th>
            <th>Year</th>
            <th>Admission Date</th>
            <th>Status</th>
        </tr>

        <%
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection("*************************************", "scott", "tiger");
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery("SELECT * FROM student WHERE status='Active' ORDER BY student_id");

            int studentCount = 0;
            while (rs.next()) {
                studentCount++;
                String studentId = rs.getString(1);
                String studentName = rs.getString(2);
                String address = rs.getString(3);
                String contactNo = rs.getString(4);
                String email = rs.getString(5);
                String photo = rs.getString(6);
                String dob = rs.getString(7);
                String admissionDate = rs.getString(8);
                String course = rs.getString(9);
                String year = rs.getString(10);
                String status = rs.getString(11);

                out.println("<tr>");
                out.println("<td>" + studentId + "</td>");
                out.println("<td>" + studentName + "</td>");
                out.println("<td>" + address + "</td>");
                out.println("<td>" + contactNo + "</td>");
                out.println("<td>" + email + "</td>");
                
                if (photo != null && !photo.trim().equals("")) {
                    out.println("<td><img src='" + photo + "' width='80' height='80'></td>");
                } else {
                    out.println("<td>No Photo</td>");
                }
                
                out.println("<td>" + dob + "</td>");
                out.println("<td>" + course + "</td>");
                out.println("<td>" + year + "</td>");
                out.println("<td>" + admissionDate + "</td>");
                out.println("<td>" + status + "</td>");
                out.println("</tr>");
            }

            if (studentCount == 0) {
                out.println("<tr><td colspan='11' align='center'>No students found</td></tr>");
            } else {
                out.println("<tr><td colspan='11' align='center'><b>Total Students: " + studentCount + "</b></td></tr>");
            }

            rs.close();
            st.close();
            con.close();

        } catch (Exception e) {
            out.println("<tr><td colspan='11' style='color:red;'>Error: " + e.getMessage() + "</td></tr>");
        }
        %>
    </table>
    
    <br><br>
    <a href="addstudent.jsp">Add New Student</a>
    <br><br>
    <a href="studentinfo.jsp">Back to Student Menu</a>
    
    </center>
</body>
</html>
