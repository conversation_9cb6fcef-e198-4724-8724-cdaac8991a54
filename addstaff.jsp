
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Staff - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            padding: 20px;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .form-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .form-grid {
            display: grid;
            gap: 25px;
        }

        .form-group {
            display: grid;
            grid-template-columns: 150px 1fr;
            gap: 15px;
            align-items: center;
        }

        .form-group label {
            color: #555;
            font-weight: 600;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea {
            padding: 12px 18px;
            border: 2px solid #E8F4FD;
            border-radius: 12px;
            font-size: 1rem;
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            transition: all 0.3s ease;
            color: #333;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4ECDC4;
            background: #FFFFFF;
            box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.1);
            transform: translateY(-2px);
        }

        .form-group input[readonly],
        .form-group input[disabled] {
            background: linear-gradient(145deg, #F0F0F0, #E8E8E8);
            color: #888;
            cursor: not-allowed;
        }

        .form-group input[type="file"] {
            padding: 8px 12px;
            background: linear-gradient(145deg, #FFF5F5, #F8FBFF);
            border: 2px dashed #FF6B9D;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #4ECDC4, #FF6B9D);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #96CEB4, #FFEAA7);
            color: #333;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #FFEAA7, #96CEB4);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(150, 206, 180, 0.3);
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 50px;
            padding: 12px 20px;
            text-decoration: none;
            color: #333;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .form-group {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-container {
                padding: 25px;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <%
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy");
        String str = sdf.format(new java.util.Date()).toString();
        session.setAttribute("date", str);
    %>

    <a href="staffinfo.jsp" class="back-button">← Back to Staff Menu</a>

    <div class="container">
        <div class="header">
            <h1>Add New Staff</h1>
            <p class="subtitle">Enter staff member details below</p>
        </div>

        <div class="form-container">
            <form action="addstaff1.jsp" method="post" enctype="multipart/form-data">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="staffId">Staff ID:</label>
                        <input type="text" id="staffId" name="t1" value="10001" readonly>
                    </div>

                    <div class="form-group">
                        <label for="staffName">Staff Name:</label>
                        <input type="text" id="staffName" name="t2" placeholder="Enter full name" required>
                    </div>

                    <div class="form-group">
                        <label for="staffAddress">Address:</label>
                        <textarea id="staffAddress" name="t3" rows="3" placeholder="Enter complete address" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="contactNo">Contact No:</label>
                        <input type="tel" id="contactNo" name="t4" placeholder="Enter phone number" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="t5" placeholder="Enter email address" required>
                    </div>

                    <div class="form-group">
                        <label for="photo">Photo:</label>
                        <input type="file" id="photo" name="t6" accept="image/*">
                    </div>

                    <div class="form-group">
                        <label for="dob">Date of Birth:</label>
                        <input type="date" id="dob" name="t7" required>
                    </div>

                    <div class="form-group">
                        <label for="joiningDate">Joining Date:</label>
                        <input type="text" id="joiningDate" name="t8" value="<%=str%>" disabled>
                    </div>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary">Register Staff</button>
                    <button type="reset" class="btn btn-secondary">Clear Form</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>