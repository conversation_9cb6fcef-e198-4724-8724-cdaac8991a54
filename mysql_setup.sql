-- MySQL Database Setup for CMS
-- Run this if Oracle is not working

CREATE DATABASE cms_db;
USE cms_db;

-- Student table
CREATE TABLE student (
    student_id VARCHAR(20) PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL,
    student_address VARCHAR(200),
    contact_no VARCHAR(15),
    email VARCHAR(100),
    photo VARCHAR(200),
    dob DATE,
    admission_date DATE,
    course VARCHAR(100),
    year VARCHAR(20),
    status VARCHAR(10) DEFAULT 'Active'
);

-- Staff table
CREATE TABLE staff (
    stid VARCHAR(20) PRIMARY KEY,
    stname VARCHAR(100) NOT NULL,
    staddress VARCHAR(200),
    contact_no VARCHAR(15),
    email VARCHAR(100),
    photo VARCHAR(200),
    dob DATE,
    joining_date DATE,
    status VARCHAR(10) DEFAULT 'Active'
);

-- Attendance table
CREATE TABLE attendance (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL,
    attendance_date DATE NOT NULL,
    subject VARCHAR(50) NOT NULL,
    status VARCHAR(10) NOT NULL CHECK (status IN ('Present', 'Absent')),
    remarks VARCHAR(200),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_attendance (student_id, attendance_date, subject)
);

-- Insert sample data
INSERT INTO student VALUES 
('20001', 'John Smith', '123 Main St', '9876543210', '<EMAIL>', '', '2000-05-15', '2023-06-01', 'B.Tech Computer Science', '2nd Year', 'Active'),
('20002', 'Jane Doe', '456 Oak Ave', '9876543211', '<EMAIL>', '', '2001-08-22', '2023-06-01', 'BCA', '3rd Year', 'Active'),
('20003', 'Mike Johnson', '789 Pine Rd', '9876543212', '<EMAIL>', '', '2000-12-10', '2023-06-01', 'B.Tech Electronics', '1st Year', 'Active'),
('20004', 'Sarah Wilson', '321 Elm St', '9876543213', '<EMAIL>', '', '2001-04-18', '2023-06-01', 'MBA', '1st Year', 'Active');

INSERT INTO staff VALUES
('10001', 'John Doe', '123 Main St, City', '123-456-7890', '<EMAIL>', 'john.jpg', '1985-05-15', '2023-01-01', 'Active'),
('10002', 'Jane Smith', '456 Oak Ave, Town', '098-765-4321', '<EMAIL>', 'jane.jpg', '1990-08-22', '2023-02-15', 'Active'),
('10003', 'Mike Johnson', '789 Pine Rd, Village', '555-123-4567', '<EMAIL>', '', '1988-12-10', '2023-03-01', 'Active'),
('10004', 'Sarah Wilson', '321 Elm St, City', '444-987-6543', '<EMAIL>', 'sarah.jpg', '1992-04-18', '2023-04-10', 'Active');

COMMIT;
