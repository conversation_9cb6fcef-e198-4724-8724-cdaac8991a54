<%@ page language="java" import="java.sql.*,javax.servlet.*,javax.servlet.http.*" %>
<html>
<body bgcolor="pink" text="blue">
<%
    try {
        String utype = request.getParameter("t1");
        String uname = request.getParameter("t2");
        String pwd = request.getParameter("t3");

        // Admin login
        if (utype.equalsIgnoreCase("Admin")) {
            if (uname.equals("admin") && pwd.equals("admin")) {
                response.sendRedirect("Admin.jsp");
            } else {
                out.println("<p>Invalid Admin Username/Password</p>");
                out.println("<a href='index.jsp'>Click Here</a> to go back");
            }
        }

        // Staff login
        else if (utype.equalsIgnoreCase("staff")) {
            if (uname.equals("staff") && pwd.equals("staff")) {
                response.sendRedirect("staffpage.jsp");
            } else {
                out.println("<p>Invalid Staff Username/Password</p>");
                out.println("<a href='index.jsp'>Click Here</a> to go back");
            }
        }

        // Student login
        else if (utype.equalsIgnoreCase("student")) {
            if (uname.equals("student") && pwd.equals("student")) {
                response.sendRedirect("studentpage.jsp");
            } else {
                out.println("<p>Invalid Student Username/Password</p>");
                out.println("<a href='index.jsp'>Click Here</a> to go back");
            }
        }

    } catch (Exception e) {
        out.println("<p style='color:red;'>Error: " + e.getMessage() + "</p>");
    }
%>
</body>
</html>
