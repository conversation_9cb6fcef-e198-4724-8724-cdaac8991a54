<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>College Management System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: float 6s ease-in-out infinite;
            max-width: 500px;
            width: 100%;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: #555;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #E8F4FD;
            border-radius: 15px;
            font-size: 1rem;
            background: linear-gradient(145deg, #FFFFFF, #F8FBFF);
            transition: all 0.3s ease;
            color: #333;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4ECDC4;
            background: #FFFFFF;
            box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.1);
            transform: translateY(-2px);
        }

        .form-group select {
            cursor: pointer;
        }

        .login-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 20px;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #4ECDC4, #FF6B9D);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
        }

        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            animation: bounce 4s ease-in-out infinite;
        }

        .circle1 {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #FFE5F1, #FF6B9D);
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .circle2 {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #E5F3FF, #4ECDC4);
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }

        .circle3 {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #F0FFE5, #96CEB4);
            bottom: 20%;
            left: 20%;
            animation-delay: 2s;
        }

        .circle4 {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #FFF5E5, #FFEAA7);
            bottom: 15%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 30px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="decorative-elements">
        <div class="circle circle1"></div>
        <div class="circle circle2"></div>
        <div class="circle circle3"></div>
        <div class="circle circle4"></div>
    </div>

    <div class="login-container">
        <div class="header">
            <h1>COLLEGE MANAGEMENT SYSTEM</h1>
            <p class="subtitle">Welcome! Please login to continue</p>
        </div>

        <form action="verify.jsp" method="post">
            <div class="form-group">
                <label for="userType">User Type</label>
                <select name="t1" id="userType" required>
                    <option value="">--SELECT USER TYPE--</option>
                    <option value="Admin">Admin</option>
                    <option value="Staff">Staff</option>
                    <option value="Student">Student</option>
                </select>
            </div>

            <div class="form-group">
                <label for="username">User Name</label>
                <input type="text" name="t2" id="username" placeholder="Enter your username" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" name="t3" id="password" placeholder="Enter your password" required>
            </div>

            <button type="submit" class="login-btn">Login</button>
        </form>
    </div>
</body>
</html>