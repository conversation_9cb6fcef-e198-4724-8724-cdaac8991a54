<%@ page import="java.sql.*" %>
<html>
<body bgcolor="pink" text="blue">
    <center>
        <h1>STAFF UPDATE FORM</h1>
        <form action="modifystaff1.jsp" method="post">
            Select Staff ID:
            <select name="t1">
                <option>---SELECT----</option>

                <%
                try {
                    Class.forName("oracle.jdbc.driver.OracleDriver");
                    Connection con = DriverManager.getConnection(
                        "*************************************", "scott", "tiger"
                    );
                    Statement st = con.createStatement();
                    ResultSet rs = st.executeQuery("SELECT stid FROM staff WHERE status='Active'");

                    while (rs.next()) {
                        String sid = rs.getString("stid");
                        out.println("<option value='" + sid + "'>" + sid + "</option>");
                    }

                    rs.close();
                    st.close();
                    con.close();

                } catch (Exception e) {
                    out.println("<option>Error: " + e.getMessage() + "</option>");
                }
                %>

            </select>
            <br><br>
            <input type="submit" value="SHOW">
        </form>
    </center>
</body>
</html>
