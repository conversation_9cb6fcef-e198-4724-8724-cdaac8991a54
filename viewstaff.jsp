<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Staff - College Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%);
            min-height: 100vh;
            padding: 20px;
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #FFE5F1 0%, #E5F3FF 25%, #F0FFE5 50%, #FFF5E5 75%, #F5E5FF 100%); }
            50% { background: linear-gradient(135deg, #F5E5FF 0%, #FFE5F1 25%, #E5F3FF 50%, #F0FFE5 75%, #FFF5E5 100%); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            background: linear-gradient(45deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            animation: gradientText 3s ease-in-out infinite;
        }

        @keyframes gradientText {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            overflow-x: auto;
        }

        .staff-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .staff-table th {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95rem;
            border: none;
        }

        .staff-table th:first-child {
            border-top-left-radius: 15px;
        }

        .staff-table th:last-child {
            border-top-right-radius: 15px;
        }

        .staff-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #E8F4FD;
            color: #333;
            font-size: 0.9rem;
        }

        .staff-table tr:nth-child(even) {
            background: linear-gradient(145deg, #F8FBFF, #FFFFFF);
        }

        .staff-table tr:nth-child(odd) {
            background: #FFFFFF;
        }

        .staff-table tr:hover {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(255, 255, 255, 0.9));
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .staff-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #4ECDC4;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .no-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 50px;
            padding: 12px 20px;
            text-decoration: none;
            color: #333;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
            color: white;
            transform: translateY(-2px);
        }

        .loading-message {
            text-align: center;
            color: #666;
            font-size: 1.1rem;
            padding: 40px;
        }

        .error-message {
            text-align: center;
            color: #dc3545;
            font-size: 1.1rem;
            padding: 40px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(220, 53, 69, 0.3);
        }

        .staff-count {
            background: linear-gradient(135deg, #96CEB4, #FFEAA7);
            color: #333;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .table-container {
                padding: 20px;
            }

            .staff-table th,
            .staff-table td {
                padding: 10px 8px;
                font-size: 0.85rem;
            }

            .staff-photo,
            .no-photo {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <%@ page import="java.sql.*" %>

    <a href="staffinfo.jsp" class="back-button">← Back to Staff Menu</a>

    <div class="container">
        <div class="header">
            <h1>👁️ View All Staff</h1>
            <p class="subtitle">Complete staff directory and information</p>
        </div>

        <div class="table-container">
            <%
                int staffCount = 0;
                try {
                    // Sample data for demonstration (replace with actual database connection)
                    String[][] sampleStaff = {
                        {"10001", "John Doe", "123 Main St, City", "123-456-7890", "<EMAIL>", "john.jpg", "1985-05-15", "2023-01-01"},
                        {"10002", "Jane Smith", "456 Oak Ave, Town", "098-765-4321", "<EMAIL>", "jane.jpg", "1990-08-22", "2023-02-15"},
                        {"10003", "Mike Johnson", "789 Pine Rd, Village", "555-123-4567", "<EMAIL>", "", "1988-12-10", "2023-03-01"},
                        {"10004", "Sarah Wilson", "321 Elm St, City", "444-987-6543", "<EMAIL>", "sarah.jpg", "1992-04-18", "2023-04-10"}
                    };

                    staffCount = sampleStaff.length;
            %>
                    <div class="staff-count">📊 Total Staff Members: <%=staffCount%></div>

                    <table class="staff-table">
                        <thead>
                            <tr>
                                <th>🆔 Staff ID</th>
                                <th>👤 Name</th>
                                <th>🏠 Address</th>
                                <th>📞 Contact</th>
                                <th>📧 Email</th>
                                <th>📷 Photo</th>
                                <th>🎂 DOB</th>
                                <th>📅 Joining Date</th>
                            </tr>
                        </thead>
                        <tbody>
            <%
                    for (String[] staff : sampleStaff) {
            %>
                            <tr>
                                <td><%=staff[0]%></td>
                                <td><%=staff[1]%></td>
                                <td><%=staff[2]%></td>
                                <td><%=staff[3]%></td>
                                <td><%=staff[4]%></td>
                                <td>
                                    <% if (staff[5] != null && !staff[5].isEmpty()) { %>
                                        <img src="<%=staff[5]%>" alt="Staff Photo" class="staff-photo">
                                    <% } else { %>
                                        <div class="no-photo">👤</div>
                                    <% } %>
                                </td>
                                <td><%=staff[6]%></td>
                                <td><%=staff[7]%></td>
                            </tr>
            <%
                    }
            %>
                        </tbody>
                    </table>
            <%
                } catch (Exception e) {
            %>
                    <div class="error-message">
                        <h3>⚠️ Error Loading Staff Data</h3>
                        <p><%=e.getMessage()%></p>
                        <p>Please check your database connection and try again.</p>
                    </div>
            <%
                }
            %>
        </div>
    </div>
</body>
</html>
